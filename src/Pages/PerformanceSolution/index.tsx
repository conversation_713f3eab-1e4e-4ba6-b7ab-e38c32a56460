/* eslint-disable */
import { AccessibleImage, Breadcrumb, Button, Modal } from '@Components/UI';
import {
  DownloadIcon,
  PdfIcon,
  UploadIcon,
  MicIcon,
  NewChatIcon,
} from '@Icons';
import PSImg from '@Assets/Images/PS.png';
import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import clsx from 'clsx';
import { useStreamedResponse } from '../Home';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Recorder from '@Components/Recorder';
import CodeOfConductModal from './CodeOfConductModal';
import { API_PATHS } from '@Helpers/Constants';
import { useDispatch, useSelector } from 'react-redux';
import Api from '@Helpers/Api';
import { setUserData } from '@Redux/SystemControl/UserControle';
import parse from 'html-react-parser';

const breadcrumbItems = [{ label: 'Home', link: '/' }, { label: 'Legal' }];

interface LegalDocObject {
  title: string;
  link: string;
}
interface LegalDraftData {
  subject: string;
  body: string;
}

const PerformanceSolution = (): JSX.Element => {
  const api = useMemo(() => new Api(), []);
  const dispatch = useDispatch();
  const userData = useSelector((state: any) => state?.UserControle?.user);
  const uploadDocumentsInputRef = useRef<HTMLInputElement | null>(null);
  const uploadDocumentsRef = useRef<HTMLInputElement | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isRecordOn, setIsRecordOn] = useState<boolean>(false);
  const [text, setText] = useState<string>('');
  const { addToast } = useToast();

  const [isFileDragging, setIsFileDragging] = useState<boolean>(false);
  const [status, setStatus] = useState<string | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showFileError, setShowFileError] = useState<boolean>(false);
  const [docs, setDocs] = useState<LegalDocObject[]>([]);
  const [showCodeOfConduct, setShowCodeOfConduct] = useState<boolean>(false);
  const [legalDraftData, setLegalDraftData] = useState<LegalDraftData | null>(null);
  const [isLegalDraftOpen, setIsLegalDraftOpen] = useState(false);
  const [hideTextArea, setHideTextArea] = useState<{
    hide: boolean;
    type: 'UPLOAD' | 'TEXT' | null;
  }>({ hide: false, type: null });

  const { mutateAsync, abort } = useStreamedResponse();

  const handleUploadClick = () => {
    uploadDocumentsInputRef.current?.click();
  };
  const validateFile = (file: File): boolean => {
    return file.type === 'application/pdf';
  };

  useEffect(() => {
    if (!userData.is_disclaimer_accepted) {
      setShowCodeOfConduct(true);
    }
  }, [userData.is_disclaimer_accepted]);

  useEffect(() => {
    if (text.trim()) {
      setHideTextArea({ ...hideTextArea, type: 'TEXT' });
    } else if (selectedFile) {
      setHideTextArea({ ...hideTextArea, type: 'UPLOAD' });
    } else {
      setHideTextArea({ ...hideTextArea, type: null });
      setDocs([])
    }
  }, [text, selectedFile]);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsFileDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsFileDragging(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsFileDragging(false);

    const file = event.dataTransfer.files[0];

    if (file && validateFile(file)) {
      handleFileUpload({
        target: { files: [file] },
      } as unknown as React.ChangeEvent<HTMLInputElement>);
    }
  }, []);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && validateFile(file)) {
      setShowFileError(false);
      setSelectedFile(file);
    } else {
      alert('Only PDF files are allowed.');
    }
  };

  const handleOnSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    try {
      const formData = new FormData();
      setIsComplete(true);
      if (selectedFile || text.trim()) {
        if (selectedFile) {
          formData.append('file', selectedFile);
        } else {
          formData.append('search_query', text);
        }

        setStatus('Loading...');

        const reader = await mutateAsync(
          'rag/vcat/ask-vcat-document/',
          formData,
          true
        );
        const decoder = new TextDecoder();
        let textBuffer = '';

        while (true) {
          const { value, done } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          textBuffer += chunk;

          let newlineIndex;
          while ((newlineIndex = textBuffer.indexOf('\n')) >= 0) {
            const rawLine = textBuffer.slice(0, newlineIndex).trim();
            textBuffer = textBuffer.slice(newlineIndex + 1);

            if (!rawLine.startsWith('data: ')) continue;

            const jsonStr = rawLine.replace(/^data:\s*/, '');
            if (!jsonStr || jsonStr === '[DONE]') continue;

            let parsedData: any;
            try {
              parsedData = JSON.parse(jsonStr);
            } catch (e) {
              console.warn('JSON parse failed, skipping:', jsonStr);
              continue;
            }

            // Now process the parsed chunk
            if (parsedData?.type === 'solution_chunk') {
              setStatus(parsedData?.message);
            } else if (parsedData?.type === 'complete') {
              if (parsedData.search_type === "legal_draft") {
                setLegalDraftData(parsedData.data);
                setIsLegalDraftOpen(true);
                setStatus(null);
                setHideTextArea({ ...hideTextArea, hide: true });
              }
              else {
                setStatus(null);
                setDocs(parsedData?.data?.links);
                setHideTextArea({ ...hideTextArea, hide: true });
              }
            } else if (
              parsedData?.type === 'status' ||
              parsedData?.type === 'metadata'
            ) {
              setStatus(parsedData?.message);
            } else if (parsedData?.type === 'error') {
              setStatus(null);
              setText('');
              setSelectedFile(null);
              if (uploadDocumentsInputRef.current) {
                uploadDocumentsInputRef.current.value = '';
              }
              addToast(
                'error',
                (parsedData?.message as string) || (parsedData?.error as string)
              );
            }
          }
        }
      } else {
        setShowFileError(true);
      }
      setIsComplete(false);
    } catch (error) {
      setStatus(null);
      setIsComplete(false);
    }
  };

  const onRecordClose = (voiceText: string) => {
    if (voiceText) {
      setText(voiceText);
      textareaRef.current?.focus();
    }
  };

  const downloadPdf = async (link: string, title: string) => {
    try {
      const res = await fetch(link, {
        method: 'GET',
        headers: { 'Content-Type': 'application/pdf' },
      });
      if (!res.ok) throw new Error(`Status ${res.status}`);
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = title + '.pdf';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      addToast('error', err as string);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (!e.shiftKey) {
        e.preventDefault();
        const form = e.currentTarget.closest('form');
        form?.requestSubmit();
      }
    }
  };

  const handleCodeOfConductAccept = async () => {
    try {
      setShowCodeOfConduct(false);
      const { data } = await api.get(API_PATHS.USER_PROFILE);
      if (data?.status) {
        dispatch(setUserData({ ...userData, ...data?.data }));
      }
    } catch (error) {
      addToast('error', error as string);
    }
  };

  const handleCopyData = async () => {
    if (!legalDraftData) return; // Add null check

    try {
      const textToCopy = `${legalDraftData.subject}\n\n${legalDraftData.body}`;
      await navigator.clipboard.writeText(textToCopy);
      alert('Data copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = `${legalDraftData.subject}\n\n${legalDraftData.body}`;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Data copied to clipboard!');
    }
  };

  const handleSendEmail = () => {
    if (!legalDraftData) return;
    const subject = encodeURIComponent(legalDraftData.subject);
    const body = encodeURIComponent(legalDraftData.body);
    const mailtoLink = `mailto:?subject=${subject}&body=${body}`;
    window.open(mailtoLink, '_blank');
  };

  return userData.is_disclaimer_accepted ? (
    <div className="flex flex-1 w-full h-full flex-col">
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Legal</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div>
          <NewChatIcon
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => {
              abort();
              setText('');
              setSelectedFile(null);
              if (uploadDocumentsInputRef.current) {
                uploadDocumentsInputRef.current.value = '';
              }
              setHideTextArea({ hide: false, type: null });
              setStatus(null);
              setDocs([]);
              setIsComplete(false);
            }}
          />
        </div>
      </div>
      <div className="p-10 pt-0">
        {isLegalDraftOpen && legalDraftData ? (
          <div className='p-10'>
            <p className="mt-2 mb-4 font-normal text-right">
              <span
                className="font-medium cursor-pointer hover:underline"
                onClick={() => {
                  setDocs([]);
                  setText('');
                  setSelectedFile(null);
                  setHideTextArea({ ...hideTextArea, type: null });
                  setLegalDraftData(null)
                  setIsLegalDraftOpen(false)
                }}
              >
                Upload or Ask Rex
              </span>
            </p>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg text-[#0FBFAD] font-semibold mb-2">Subject:</h3>
                <p className="text-gray-700 bg-gray-50 p-3">
                  {legalDraftData.subject}
                </p>
              </div>
              <div>
                <h3 className="text-lg text-[#0FBFAD]  font-semibold mb-2">Message:</h3>
                <div className="text-gray-700 bg-gray-50 p-4 whitespace-pre-wrap">
                  {legalDraftData.body}
                </div>
              </div>
            </div>
            <div className="flex gap-4 mt-6 justify-center">
              <button
                onClick={handleCopyData}
                className="px-6 py-2 bg-[#0FBFAD] text-white rounded-lg hover:bg-[#0da396] transition-colors duration-200 flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Copy Data
              </button>

              <button
                onClick={handleSendEmail}
                className="px-6 py-2 bg-[#0FBFAD] text-white rounded-lg hover:bg-[#0da396] transition-colors duration-200 flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Send Email
              </button>
            </div>
          </div>
        ) : (
          <div className="flex w-full border border-b-primary rounded-2xl">
            <form className="flex flex-1 flex-col p-8" onSubmit={handleOnSubmit}>

              {docs.length > 0 ? (
                <>
                  <p className="mt-2 mb-4 font-normal text-center">
                    <span
                      className="font-medium cursor-pointer hover:underline"
                      onClick={() => {
                        setDocs([]);
                        setText('');
                        setSelectedFile(null);
                        setHideTextArea({ ...hideTextArea, type: null });
                        setLegalDraftData(null)
                        setIsLegalDraftOpen(false)
                      }}
                    >
                      Upload or Ask Rex
                    </span>
                  </p>

                  <div className="flex flex-col gap-4 py-4">
                    {docs.map((el: LegalDocObject, index) => (
                      <div
                        key={index}
                        className="w-full flex py-2 border border-teal-200 rounded-lg flex-row px-6 text-primary-100 items-center gap-x-2"
                      >
                        <PdfIcon height={16} width={16} />
                        <div className="relative group flex-1">
                          <div className="truncate overflow-hidden whitespace-nowrap">
                            {el.title}
                          </div>
                          <div className="absolute bottom-full left-0 mb-1 hidden w-max max-w-xs rounded bg-gray-800 px-2 py-1 text-xs text-white shadow-lg group-hover:block z-50">
                            {el.title}
                          </div>
                        </div>
                        <DownloadIcon
                          height={16}
                          width={16}
                          className="cursor-pointer"
                          onClick={() => downloadPdf(el.link, el.title)}
                        />
                      </div>
                    ))}
                  </div>
                </>
              )
                :
                (
                  <>
                    {(hideTextArea.type === null || hideTextArea.type === 'TEXT') && (
                      <>
                        <p className="text-md font-bold pb-10 text-gray-500">Rex can help find relevant past VCAT cases—just type in what you need and Rex will search for similar decisions</p>
                        <div className="flex flex-row p-1 border w-full max-h-36 group items-end px-2 rounded-[10px] border-b-primary focus-within:border-primary-100 focus-within:shadow-primary">
                          <textarea
                            ref={textareaRef}
                            className="w-full p-2 text-lg border-none outline-none resize-none bg-transparent max-h-30 custom-scrollbar disabled:cursor-not-allowed"
                            rows={3}
                            placeholder="Ask REX Any Legal Questions..."
                            value={text}
                            onChange={(e) => {
                              setText(e.target.value);
                              if (e.target.value.trim() || selectedFile) {
                                setShowFileError(false);
                              }
                            }}
                            onKeyDown={handleKeyDown}
                            autoFocus
                            disabled={!!selectedFile}
                          />
                          <MicIcon
                            height={20}
                            width={20}
                            className={`cursor-pointer hover:scale-110 ${selectedFile ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => {
                              if (!selectedFile) setIsRecordOn(true);
                            }}
                          />
                        </div>
                        {!hideTextArea.type && (
                          <div className="flex items-center justify-center w-full my-2">
                            <p className="px-2 text-center text-gray-500">- or -</p>
                          </div>
                        )}
                      </>
                    )}

                    {(hideTextArea.type === null || hideTextArea.type === 'UPLOAD') && (
                      <>
                        <div
                          ref={uploadDocumentsRef}
                          onDragOver={handleDragOver}
                          onDrop={handleDrop}
                          onDragLeave={handleDragLeave}
                          className={clsx(
                            'pb-6 pt-6 w-full flex flex-col bg-[#f1f1f1] items-center text-center rounded-xl cursor-pointer',
                            text ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                            isFileDragging &&
                            'border border-dashed border-primary-100 bg-primary-light scale-105'
                          )}
                          onClick={handleUploadClick}
                        >
                          <input
                            ref={uploadDocumentsInputRef}
                            type="file"
                            className="sr-only"
                            accept="application/pdf"
                            onChange={handleFileUpload}
                            multiple={false}
                            disabled={!!text}
                          />
                          <UploadIcon height={24} width={24} />
                          <div className="pb-2 pt-3 font-semibold">Upload</div>
                          <div className="text-secondary w-1/2 text-center">
                            Upload any legal document
                          </div>
                          {selectedFile && (
                            <div className="py-1.5 mt-4 mx-2 rounded-[10px] shadow-primary bg-white flex flex-row w-fit px-6 text-primary-100 items-center gap-x-2">
                              <PdfIcon height={18} width={18} />
                              <div>{selectedFile?.name}</div>
                            </div>
                          )}
                        </div>
                        {showFileError && (
                          <p className="text-red-500 text-sm break-words">
                            Please upload your case.
                          </p>
                        )}
                      </>
                    )}

                    <div className="flex pt-2 flex-col">
                      <Button
                        type="submit"
                        text="Submit"
                        loading={isComplete}
                        className="mt-3"
                      />
                    </div>

                    {status && (
                      <div className="mt-2 text-md font-medium text-gray-900">
                        <span className="animate-left-to-right-pulse">{status}</span>
                      </div>
                    )}
                  </>
                )
              }



            </form>

            {isRecordOn && (
              <Recorder setIsRecordOn={setIsRecordOn} onClose={onRecordClose} />
            )}

            <div className="flex flex-1 flex-col">
              <AccessibleImage
                src={PSImg}
                alt="Performance solution image"
                className="h-full w-full"
              />
            </div>

          </div>
        )
        }
      </div>

    </div >
  ) : (
    <>
      <CodeOfConductModal
        isOpen={showCodeOfConduct}
        onAccept={handleCodeOfConductAccept}
      />
    </>
  );
};

export default PerformanceSolution;
